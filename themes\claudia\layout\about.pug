extends widget/base

block append head
    link(rel='stylesheet' href= url_for('/style/about.css'))

block content
    main.about-page.is-flex.is-align-items-center.content.is-full-height
        .container.is-max-widescreen.px-2
            .columns.is-marginless
                aside.column.is-4.is-flex.is-flex-direction-column.is-justify-content-center
                    img.js-img-fadeIn(src= theme.about_pic)
                section.column.is-8.is-flex.is-flex-direction-column.is-justify-content-center
                    h2.about-title= __('about')
                    article!= page.content
block append script
    script.
        $claudia.fadeInImage()
