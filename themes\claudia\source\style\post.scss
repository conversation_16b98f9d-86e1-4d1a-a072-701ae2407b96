@use "common/variable" as *;

$lineColor: var(--border-line-color);

#postTopic {
  cursor: pointer;
  will-change: transform;
  transform: translateY(100%);

  p {
    color: var(--primary-text-color);
  }

  &.is-show-scrollToTop-tips {
    transition: transform 300ms linear 300ms;
    transform: translateY(-100%);
    //background: red;
  }

  &.is-flash-scrollToTop-tips {
    transition: transform 300ms linear 900ms;
    transform: translateY(0);
  }

  &.is-switch-post-title {
    transition: none;
    transform: translateY(0);
    //background: blue;
  }

  &.is-show-post-title {
    transition: transform 300ms linear;
    transform: translateY(0);
    //background: darkorchid;
  }

  &.is-hidden-topic-bar {
    transition: transform 100ms linear;
    transform: translateY(100%);
    //background: #00c4a7;
  }

  &.immediately-show {
    transition: none;
    transform: translateY(0);
    //background: #545454;
  }
}

.post-page {
  .post-content {

    code {
      border-radius: 4px;
      background: var(--third-bg-color);
    }

    pre {
      padding: 0;
      background: transparent;
      text-align: left;
      -moz-tab-size: 2;
      tab-size: 2;

      code {
        padding: 15px;
        border: 1px solid $lineColor;
        background: var(--pre-code-bg-color);
      }
    }

    .hljs {
      color: var(--second-text-color);
    }

    img {
      display: block;
      margin: 0 auto;
      max-height: 500px;

      border-radius: $borderRadius;
      box-shadow: 0 0 15px rgba(0, 0, 0, .05);

      opacity: 0;
      will-change: opacity;
    }

    a {
      color: $activeColor;
    }

    // Enhanced code block styles for Hexo SSR markup (figure.highlight)
    figure.highlight {
      margin: 1.25rem 0;
      border: 1px solid $lineColor;
      border-radius: $borderRadius;
      background: var(--pre-code-bg-color);
      overflow: hidden;
      position: relative;

      table {
        width: 100%;
        border-collapse: collapse;
      }

      .gutter {
        user-select: none;
        background: var(--third-bg-color);
        color: var(--text-color-light);
        border-right: 1px solid $lineColor;

        pre {
          padding: 15px 2px;
          text-align: right;
          min-width: 1.25rem;
          line-height: 1.65;
        }
      }

      .code {
        pre {
          padding: 15px;
          overflow-x: auto;
          text-align: left;
          -moz-tab-size: 2;
          tab-size: 2;
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
          line-height: 1.65;
          color: var(--second-text-color);
          background: transparent;
        }
      }

      .code-lang {
        position: absolute;
        bottom: 8px;
        right: 12px;
        font-size: 12px;
        color: var(--text-color-light);
        background: transparent;
      }

      .code-copy-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 12px;
        padding: 4px 8px;
        border: 1px solid $lineColor;
        border-radius: 4px;
        background: var(--third-bg-color);
        color: var(--second-text-color);
        cursor: pointer;
        transition: opacity .2s ease;
        opacity: 0;
      }

      .code-copy-btn:active {
        transform: translateY(1px);
      }

      &:hover .code-copy-btn,
      .code-copy-btn:focus {
        opacity: 1;
      }

      .code-lang {
        pointer-events: none;
      }
    }

    // Token colors for server-side highlighted markup (no .hljs- prefix)
    figure.highlight pre {
      .comment,
      .quote {
        color: #696969;
      }

      .variable,
      .template-variable,
      .tag,
      .name,
      .selector-id,
      .selector-class,
      .regexp,
      .deletion {
        color: #d91e18;
      }

      .number,
      .built_in,
      .builtin-name,
      .literal,
      .type,
      .params,
      .meta,
      .link {
        color: #aa5d00;
      }

      .attribute {
        color: #aa5d00;
      }

      .string,
      .symbol,
      .bullet,
      .addition {
        color: #008000;
      }

      .title,
      .section {
        color: #007faa;
      }

      .keyword,
      .selector-tag {
        color: #7928a1;
      }

      .emphasis {
        font-style: italic;
      }

      .strong {
        font-weight: bold;
      }
    }
  }

  .jump-container .button{
    max-width: calc(50% - 5px);
    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis;
    }
  }

  .comment-container {
    border-top: 1px solid $lineColor;
  }

  .toc {
    position: sticky;
    top: 60px;

    margin-left: 0;
    margin-right: 0;
    padding-left: 15px;
    height: calc(100vh - 100px);

    overflow: auto;
    list-style: none!important;
    border-left: 1px solid $lineColor;

    &::-webkit-scrollbar {
      display: none;
    }

    ol {
      margin-top: 5px;
      margin-left: 15px;
      list-style: none!important;
    }

    .is-active {
      span {
        color: $activeColor!important;
      }
    }
  }

  :target {
    padding-top: 60px;
    margin-top: -60px!important;
  }
}
