@use "common/variable" as *;

/**
 * Mermaid diagram styles for Claudia theme
 * Provides responsive and theme-integrated styling for Mermaid diagrams
 */

// Mermaid container styles
.mermaid-container {
  margin: 2rem 0;
  padding: 1rem;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow-x: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  
  @media (max-width: 768px) {
    margin: 1rem 0;
    padding: 0.5rem;
    border-radius: 4px;
  }
}

// Mermaid diagram wrapper
.mermaid-diagram {
  display: block;
  min-height: 100px;
  position: relative;
  max-width: min(900px, 100%);
  margin: 0 auto;
  
  // Ensure diagrams are responsive
  svg {
    display: block;
    margin: 0 auto;
    max-width: 100% !important;
    height: auto !important;
    
    // Fix for mobile devices
    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Loading state
.mermaid-diagram:not([data-processed]) {
  &::before {
    content: "Loading diagram...";
    display: block;
    text-align: center;
    color: var(--text-color-light);
    font-style: italic;
    padding: 2rem;
  }
}

// Error state
.mermaid-diagram.error {
  &::before {
    content: "Error rendering diagram";
    color: var(--error-color, #f56565);
  }
}

// Theme-specific overrides for better integration
.mermaid {
  // Flowchart styles
  .node rect,
  .node circle,
  .node ellipse,
  .node polygon {
    fill: var(--mermaid-node-bg, #f9f9f9);
    stroke: var(--mermaid-node-border, #409eff);
    stroke-width: 2px;
  }
  
  .node .label {
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Edge styles
  .edgePath .path {
    stroke: var(--mermaid-edge-color, #409eff);
    stroke-width: 2px;
  }
  
  .edgeLabel {
    background-color: var(--mermaid-label-bg, #fff);
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
    font-size: 12px;
  }
  
  // Sequence diagram styles
  .actor {
    fill: var(--mermaid-actor-bg, #eee);
    stroke: var(--mermaid-actor-border, #409eff);
  }
  
  .actor-line {
    stroke: var(--mermaid-line-color, #999);
  }
  
  .messageLine0,
  .messageLine1 {
    stroke: var(--mermaid-message-color, #409eff);
  }
  
  .messageText {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Gantt chart styles
  .section0,
  .section1,
  .section2,
  .section3 {
    fill: var(--mermaid-section-bg, #f9f9f9);
  }
  
  .task0,
  .task1,
  .task2,
  .task3 {
    fill: var(--mermaid-task-bg, #409eff);
  }
  
  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Git graph styles
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
}

// Dark theme adjustments
.appearance-dark, [data-theme="dark"] {
  --mermaid-node-bg: #2d3748;
  --mermaid-node-border: #63b3ed;
  --mermaid-text-color: #e2e8f0;
  --mermaid-edge-color: #63b3ed;
  --mermaid-label-bg: #1a202c;
  --mermaid-actor-bg: #2d3748;
  --mermaid-actor-border: #63b3ed;
  --mermaid-line-color: #718096;
  --mermaid-message-color: #63b3ed;
  --mermaid-section-bg: #2d3748;
  --mermaid-task-bg: #63b3ed;
  
  .mermaid-container {
    background: var(--post-bg-color-dark, #1a202c);
    border-color: var(--border-color-dark, #2d3748);
  }
}

/* Light theme glassmorphism with higher contrast for readability */
:root.appearance-light {
  .mermaid-modal {
    background: rgba(255, 255, 255, 0.88);
    border: 1px solid rgba(0, 0, 0, 0.08);
    color: var(--primary-text-color);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.18), inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(18px) saturate(140%);
    -webkit-backdrop-filter: blur(18px) saturate(140%);
  }
  .mermaid-modal-toolbar {
    background: rgba(255, 255, 255, 0.66);
    border-bottom-color: rgba(0, 0, 0, 0.06);
  }
  .mermaid-modal .mermaid-btn {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 0, 0, 0.08);
    color: var(--primary-text-color);
  }
  .mermaid-modal .mermaid-btn:hover {
    background: rgba(255, 255, 255, 1);
  }
}

/* Auto appearance: default to light tweaks; dark handled above by appearance-dark */
:root.appearance-auto {
  .mermaid-modal {
    background: rgba(255, 255, 255, 0.88);
    border: 1px solid rgba(0, 0, 0, 0.08);
    color: var(--primary-text-color);
  }
  .mermaid-modal-toolbar {
    background: rgba(255, 255, 255, 0.66);
    border-bottom-color: rgba(0, 0, 0, 0.06);
  }
  .mermaid-modal .mermaid-btn {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 0, 0, 0.08);
    color: var(--primary-text-color);
  }
  .mermaid-modal .mermaid-btn:hover {
    background: rgba(255, 255, 255, 1);
  }
  @media (prefers-color-scheme: dark) {
    .mermaid-modal {
      background: rgba(26, 32, 44, 0.55);
      border: 1px solid rgba(255, 255, 255, 0.12);
      color: var(--second-text-color);
    }
    .mermaid-modal-toolbar {
      background: rgba(26, 32, 44, 0.35);
      border-bottom-color: rgba(255, 255, 255, 0.12);
    }
    .mermaid-modal .mermaid-btn {
      background: rgba(26, 32, 44, 0.45);
      border-color: rgba(255, 255, 255, 0.12);
      color: var(--second-text-color);
    }
    .mermaid-modal .mermaid-btn:hover {
      background: rgba(26, 32, 44, 0.58);
    }
  }
}

// High contrast mode adjustments
@media (prefers-contrast: high) {
  .mermaid {
    .node rect,
    .node circle,
    .node ellipse,
    .node polygon {
      stroke-width: 3px;
    }
    
    .edgePath .path {
      stroke-width: 3px;
    }
  }
}

// Print styles
@media print {
  .mermaid-container {
    background: white !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .mermaid-diagram svg {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}

// Animation for diagram loading
.mermaid-diagram[data-processed] {
  animation: fadeInDiagram 0.3s ease-in-out;
}

@keyframes fadeInDiagram {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Inline toolbar styles
.mermaid-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 4px 6px;
  z-index: 1;
  backdrop-filter: saturate(120%) blur(6px);
}

.mermaid-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: var(--third-bg-color);
  color: var(--second-text-color);
  cursor: pointer;
}

.mermaid-btn:hover {
  background: var(--primary-bg-color);
  color: var(--primary-text-color);
}

.mermaid-zoom-indicator { display: none; }

// Modal overlay and dialog
.mermaid-modal-overlay {
  position: fixed;
  inset: 0;
  /* Dim with subtle tint; enable blur of page behind */
  background: rgba(10, 10, 10, 0.35);
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.mermaid-modal-overlay.is-active { display: flex; }

.mermaid-modal-open { overflow: hidden; }

.mermaid-modal {
  /* Glassmorphism card */
  background: rgba(255, 255, 255, 0.22);
  border: 1px solid rgba(255, 255, 255, 0.30);
  color: var(--second-text-color);
  width: min(96vw, 1200px);
  height: min(92vh, 900px);
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0,0,0,.28), inset 0 1px 0 rgba(255,255,255,0.18);
  backdrop-filter: blur(18px) saturate(140%);
  -webkit-backdrop-filter: blur(18px) saturate(140%);
  display: flex;
  flex-direction: column;
  outline: none;
}

.mermaid-modal-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.12);
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
}

.mermaid-toolbar-spacer { flex: 1; }

.mermaid-modal-body {
  position: relative;
  flex: 1;
  overflow: auto;
  padding: 10px;
}

.mermaid-modal-diagram {
  min-height: 100%;
}

/* Buttons in modal toolbar - subtle glass buttons */
.mermaid-modal .mermaid-btn {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--second-text-color);
}

.mermaid-modal .mermaid-btn:hover {
  background: rgba(255, 255, 255, 0.35);
}

/* Dark theme tweaks for glassmorphism */
[data-theme="dark"] {
  .mermaid-modal {
    background: rgba(26, 32, 44, 0.55); /* ~ #1a202c */
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow: 0 10px 40px rgba(0,0,0,.45), inset 0 1px 0 rgba(255,255,255,0.08);
  }
  .mermaid-modal-toolbar {
    background: rgba(26, 32, 44, 0.35);
    border-bottom-color: rgba(255, 255, 255, 0.12);
  }
  .mermaid-modal .mermaid-btn {
    background: rgba(26, 32, 44, 0.45);
    border-color: rgba(255, 255, 255, 0.12);
  }
  .mermaid-modal .mermaid-btn:hover {
    background: rgba(26, 32, 44, 0.58);
  }
}