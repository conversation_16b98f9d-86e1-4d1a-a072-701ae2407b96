/**
 * Mermaid diagram styles for <PERSON> theme
 * Provides responsive and theme-integrated styling for Mermaid diagrams
 */
.mermaid-container {
  position: relative;
  margin: 2rem 0;
  padding: 1rem;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: visible;
}
@media (max-width: 768px) {
  .mermaid-container {
    margin: 1rem 0;
    padding: 0.5rem;
    border-radius: 4px;
  }
}

.mermaid-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}
.mermaid-container:hover .mermaid-toolbar {
  opacity: 1;
}

.mermaid-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: all 0.2s ease;
}
.mermaid-btn:hover {
  background: var(--hover-bg-color, #f5f5f5);
  border-color: var(--primary-color, #409eff);
  color: var(--primary-color, #409eff);
}
.mermaid-btn:active {
  transform: scale(0.95);
}
.mermaid-btn i {
  font-style: normal;
  font-weight: bold;
}

.mermaid-diagram-wrapper {
  overflow: auto;
  transition: transform 0.2s ease, margin 0.2s ease;
  transform-origin: center center;
}

.mermaid-diagram {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}
.mermaid-diagram svg {
  max-width: 100% !important;
  height: auto !important;
}
@media (max-width: 768px) {
  .mermaid-diagram svg {
    font-size: 12px;
  }
}

.mermaid-diagram:not([data-processed])::before {
  content: "Loading diagram...";
  display: block;
  text-align: center;
  color: var(--text-color-light);
  font-style: italic;
  padding: 2rem;
}

.mermaid-diagram.error::before {
  content: "Error rendering diagram";
  color: var(--error-color, #f56565);
}

.mermaid .node rect,
.mermaid .node circle,
.mermaid .node ellipse,
.mermaid .node polygon {
  fill: var(--mermaid-node-bg, #f9f9f9);
  stroke: var(--mermaid-node-border, #409eff);
  stroke-width: 2px;
}
.mermaid .node .label {
  color: var(--mermaid-text-color, #333);
  font-family: var(--font-family);
}
.mermaid .edgePath .path {
  stroke: var(--mermaid-edge-color, #409eff);
  stroke-width: 2px;
}
.mermaid .edgeLabel {
  background-color: var(--mermaid-label-bg, #fff);
  color: var(--mermaid-text-color, #333);
  font-family: var(--font-family);
  font-size: 12px;
}
.mermaid .actor {
  fill: var(--mermaid-actor-bg, #eee);
  stroke: var(--mermaid-actor-border, #409eff);
}
.mermaid .actor-line {
  stroke: var(--mermaid-line-color, #999);
}
.mermaid .messageLine0,
.mermaid .messageLine1 {
  stroke: var(--mermaid-message-color, #409eff);
}
.mermaid .messageText {
  fill: var(--mermaid-text-color, #333);
  font-family: var(--font-family);
}
.mermaid .section0,
.mermaid .section1,
.mermaid .section2,
.mermaid .section3 {
  fill: var(--mermaid-section-bg, #f9f9f9);
}
.mermaid .task0,
.mermaid .task1,
.mermaid .task2,
.mermaid .task3 {
  fill: var(--mermaid-task-bg, #409eff);
}
.mermaid .taskText0,
.mermaid .taskText1,
.mermaid .taskText2,
.mermaid .taskText3 {
  fill: var(--mermaid-text-color, #333);
  font-family: var(--font-family);
}
.mermaid .commit-id,
.mermaid .commit-msg,
.mermaid .branch-label {
  fill: var(--mermaid-text-color, #333);
  font-family: var(--font-family);
}

[data-theme=dark] {
  --mermaid-node-bg: #2d3748;
  --mermaid-node-border: #63b3ed;
  --mermaid-text-color: #e2e8f0;
  --mermaid-edge-color: #63b3ed;
  --mermaid-label-bg: #1a202c;
  --mermaid-actor-bg: #2d3748;
  --mermaid-actor-border: #63b3ed;
  --mermaid-line-color: #718096;
  --mermaid-message-color: #63b3ed;
  --mermaid-section-bg: #2d3748;
  --mermaid-task-bg: #63b3ed;
  --hover-bg-color: #2d3748;
}
[data-theme=dark] .mermaid-container {
  background: var(--post-bg-color-dark, #1a202c);
  border-color: var(--border-color-dark, #2d3748);
}
[data-theme=dark] .mermaid-btn {
  background: var(--post-bg-color-dark, #1a202c);
  border-color: var(--border-color-dark, #2d3748);
  color: var(--text-color-dark, #e2e8f0);
}
[data-theme=dark] .mermaid-btn:hover {
  background: var(--hover-bg-color, #2d3748);
}
[data-theme=dark] .mermaid-modal {
  background: rgba(0, 0, 0, 0.9);
}
[data-theme=dark] .mermaid-modal-content {
  background: var(--post-bg-color-dark, #1a202c);
}
[data-theme=dark] .mermaid-modal-header {
  border-bottom-color: var(--border-color-dark, #2d3748);
}

@media (prefers-contrast: high) {
  .mermaid .node rect,
  .mermaid .node circle,
  .mermaid .node ellipse,
  .mermaid .node polygon {
    stroke-width: 3px;
  }
  .mermaid .edgePath .path {
    stroke-width: 3px;
  }
}
@media print {
  .mermaid-container {
    background: white !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  .mermaid-diagram svg {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}
.mermaid-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.mermaid-modal.show {
  opacity: 1;
  visibility: visible;
}

.mermaid-modal-content {
  background: var(--post-bg-color);
  border-radius: 8px;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}
.mermaid-modal.show .mermaid-modal-content {
  transform: scale(1);
}

.mermaid-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
}

.mermaid-modal-toolbar {
  display: flex;
  gap: 8px;
}
.mermaid-modal-toolbar .mermaid-btn {
  opacity: 1;
}
.mermaid-modal-toolbar .mermaid-btn.mermaid-close {
  background: #ff4757;
  border-color: #ff4757;
  color: white;
}
.mermaid-modal-toolbar .mermaid-btn.mermaid-close:hover {
  background: #ff3838;
  border-color: #ff3838;
}

.mermaid-modal-body {
  flex: 1;
  padding: 20px;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mermaid-modal-diagram-wrapper {
  transition: transform 0.2s ease;
  transform-origin: center center;
}
.mermaid-modal-diagram-wrapper .mermaid-modal-diagram {
  display: flex;
  justify-content: center;
  align-items: center;
}
.mermaid-modal-diagram-wrapper .mermaid-modal-diagram svg {
  max-width: none !important;
  max-height: none !important;
}

.mermaid-diagram[data-processed] {
  animation: fadeInDiagram 0.3s ease-in-out;
}

@keyframes fadeInDiagram {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@media (max-width: 768px) {
  .mermaid-toolbar {
    top: 4px;
    right: 4px;
    gap: 2px;
    opacity: 1;
  }
  .mermaid-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  .mermaid-modal-content {
    width: 95vw;
    height: 95vh;
    border-radius: 4px;
  }
  .mermaid-modal-header {
    padding: 12px 16px;
  }
  .mermaid-modal-body {
    padding: 16px;
  }
  .mermaid-modal-toolbar {
    gap: 4px;
  }
  .mermaid-modal-toolbar .mermaid-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}
@media (max-width: 1024px) and (min-width: 769px) {
  .mermaid-modal-content {
    width: 92vw;
    height: 92vh;
  }
}
@media (hover: none) and (pointer: coarse) {
  .mermaid-toolbar {
    opacity: 1;
  }
  .mermaid-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  .mermaid-btn:hover {
    transform: none;
  }
  .mermaid-btn:active {
    transform: scale(0.9);
    background: var(--primary-color, #409eff);
    color: white;
  }
}

/*# sourceMappingURL=mermaid.css.map */
